import asyncio
import pl


START_URL = "https://www.twitch.tv/"

async def safe_get(page, url, timeout=10):
    try:
        return await asyncio.wait_for(
            page.request.get(url),
            timeout=timeout
        )
    except asyncio.TimeoutError:
        return None
    
async def get_page(page, page_url):
    try:
        resp = await safe_get(page, page_url)
        if resp is None:
            return None
        # save page
        with open(f"twitch/{page_url.split('/')[-1]}.html", 'w', encoding='utf-8') as f:
            f.write(await resp.text())
        return await resp.text()
    except Exception as e:
        print(f"Error: {e}")
        return None

async def login_twitch():
    profile = {
        'username': 'talksimilarart',
        'password': 'irritablenightwraith'
    }
    print("starting")
    try:
        async with AsyncCamoufox(headless=False, window=(1280, 720), humanize=True) as browser:
            page = await browser.new_page()
            print(browser.version)
            await page.goto(START_URL)


            await page.click('button[data-a-target="login-button"]')
            await asyncio.sleep(1)
            await page.type('input#login-username', profile['username'])
            await asyncio.sleep(1)
            await page.type('input#password-input', profile['password'])
            await asyncio.sleep(1)
            await page.click('button[data-a-target="passport-login-button"]')
            await asyncio.sleep(1)

            a = input("waiting")
            await get_page(page, "https://www.twitch.tv/directory/category/just-chatting")
            a = input("waiting 2")
            await browser.close()

            

    except Exception as e:
        print(f"Error: {e}")
    

async def main():
    await login_twitch()

if __name__ == '__main__':
    asyncio.run(main())
