import asyncio
from generate_steam.utils.generate_identity import IdentityG<PERSON>, generate_id
asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

from camoufox.async_api import AsyncCamoufox
from generate_steam.utils.get_email_hyperlink import get_verification_link
from human_id import generate_id

from generate_steam.utils.get_email_hyperlink import get_log_in_code
from generate_steam.utils.get_email_hyperlink import get_disable_link
from generate_steam.utils.mail_util import add_user


START_URL = "https://store.steampowered.com/join/"

async def add_profile(email, username, password):
    # write profile to a text file
    with open('generate_steam/profiles.txt', 'a') as f:
        f.write(f"{email},{username},{password}\n")

async def fill_first_page(page, email):
    cookie_clicked = False
    try:
        await page.wait_for_selector('#acceptAllButton', state='visible', timeout=10000)
        await page.click('#acceptAllButton')
        print("Accepted cookies")
        cookie_clicked = True
    except:
        print("No cookie popup")

    try:
        await page.wait_for_selector('input[name="email"]', state='visible', timeout=10000)
    except:
        print("No email input")
        a = input("press enter to continue")
        return
    
    await page.type('input[name="email"]', email)
    await asyncio.sleep(1)
    print("filled email")

    try:
        await page.wait_for_selector('input[name="reenter_email"]', state='visible', timeout=10000)
    except:
        print("No confirm email input")
        a = input("press enter to continue")
        return

    await page.type('input[name="reenter_email"]', email)
    await asyncio.sleep(1)
    print("filled confirm email")

    if not cookie_clicked:
        try:
            await page.wait_for_selector('#acceptAllButton', state='visible', timeout=10000)
            await page.click('#acceptAllButton')
            print("Accepted cookies")
        except:
            print("No cookie popup")

    # await page.click('div#checkbox')
    # await asyncio.sleep(1)
    a = input("click capture")

    try:
        await page.wait_for_selector('input#i_agree_check', state='visible', timeout=10000)
    except:
        print("No agree checkbox")
        a = input("press enter to continue")
        return

    await page.click('input#i_agree_check')
    print("clicked agree")
    await asyncio.sleep(1)

    try:
        await page.wait_for_selector('button#createAccountButton', state='visible', timeout=10000)
    except:
        print("No continue button")
        a = input("press enter to continue")
        return

    await page.click('button#createAccountButton')
    print("clicked continue")
    await asyncio.sleep(1)

async def verify_email(browser, email):
    # get verification link
    link = await get_verification_link(email)
    print(f"Verification link: {link}")
    verify_page = await browser.new_page()
    await verify_page.goto(link)
    await asyncio.sleep(2)
    await verify_page.close()
    await asyncio.sleep(2)

async def fill_second_page(page, username, password):
    # fill in username, password, confirm password, and click done
    await page.type('input[name="accountname"]', username)
    await asyncio.sleep(1)
    await page.type('input[name="password"]', password)
    # await page.type('input[name="password"]', password)
    await asyncio.sleep(1)
    await page.type('input[name="reenter_password"]', password)
    await asyncio.sleep(1)
    await page.click('button#createAccountButton')
    await asyncio.sleep(1)

async def get_disable_link_wrapper(browser, email):
    attempts = 0
    while attempts < 3:
        try:
            link = await get_disable_link(email)
            if link != None:
                break
        except:
            attempts += 1
            print("Failed to get disable link, retrying")
            await asyncio.sleep(10)
    if attempts == 3:
        raise Exception("Failed to get disable link")
    
    print(f"Disable link: {link}")
    disable_page = await browser.new_page()
    await disable_page.goto(link)
    await asyncio.sleep(2)
    await disable_page.close()
    await asyncio.sleep(2)


async def log_out(page):
    await page.click('button#account_pulldown')
    await asyncio.sleep(1)
    await page.click('a.popup_menu_item:has-text("Sign out")')
    await asyncio.sleep(5)

async def log_in(page, email, username, password):
    await page.goto("https://steamcommunity.com/login/home/<USER>")
    await asyncio.sleep(1)
    await page.type('div:has-text("Sign in with account name") input', username)
    await asyncio.sleep(1)
    await page.type('div:has(> div:has-text("Password")) input[type="password"]', password)
    await asyncio.sleep(1)
    await page.click('button:has-text("Sign in")')
    await asyncio.sleep(1)
    print("getting code")
    # there isn't always a code
    try:
        code = await get_log_in_code(email)
        await page.type('input#auth_code', code)
        await asyncio.sleep(1)
        await page.click('button:has-text("Continue")')
    except:
        print("No code")
        return

async def disable_two_factor_auth(page):
    print("Disabling two factor auth")
    await page.goto("https://store.steampowered.com/account/?snr=1_4_4__global-header")
    await asyncio.sleep(1)
    await page.goto("https://store.steampowered.com/account/authorizeddevices")
    await asyncio.sleep(1)
    await page.click('a:has-text("Remove security")')
    await asyncio.sleep(1)
    async with page.expect_navigation():
        await page.check('#none_authenticator_check')
    await asyncio.sleep(1)
    await page.click('a:has-text("Disable Steam Guard")')
    await asyncio.sleep(1)


async def create_account():
    profile = IdentityGen()
    print(profile['email'])
    add_user(email=profile['email'])

    try:
        async with AsyncCamoufox(headless=False, window=(1280, 720), humanize=True) as browser:
            page = await browser.new_page()
            await page.goto(START_URL)

            await fill_first_page(page, profile['email'])

            await verify_email(browser, profile['email'])

            await fill_second_page(page, profile['username'], profile['password'])

            error = page.locator('#error_display')
            if await error.is_visible():
                print(await error.inner_text())
                print("Error with password")
                new_password = generate_id(separator='', word_count=3)[:8] + 'A1!'
                print(f"New password: {new_password}")
                profile['password'] = new_password
                await fill_second_page(page, profile['username'], profile['password'])

            await log_out(page)
            await log_in(page, profile['email'], profile['username'], profile['password'])
            await disable_two_factor_auth(page)
            await get_disable_link_wrapper(browser, profile['email'])
            
            await add_profile(profile['email'], profile['username'], profile['password'])
            
            await asyncio.sleep(5)

            # a = input("press enter to close")
            await browser.close()

            

    except Exception as e:
        print(f"Error: {e}")
    

async def main():
    await create_account()

if __name__ == '__main__':
    asyncio.run(main())
